import sys
import os
import program.RainbowV1 as Rb
import schedule
import time
import datetime
import shutil
import threading
import subprocess
import psutil
from program.exchange.exchange_api import ExchangeAPI

"""
建议使用bat命令自动调用本程序，实现自动化交易
bat命令的编写建议参考：
https://bbs.quantclass.cn/thread/40437

本程序需要的所有包都在xbx-py11中，安装命令：
pip install xbx-py11
"""
root_path = os.path.dirname(os.path.abspath(__file__))
python_exe = sys.executable


def save_flag(path, msg):
    # 在指定的路径保存一个txt文件信息
    with open(path, 'a', encoding='utf-8') as f:
        f.write(f'{datetime.datetime.now()}：{msg}\n')


def select_stock(code_name):
    folder_path = root_path + '/data/系统缓存/调度信息'
    os.makedirs(folder_path, exist_ok=True)
    flag_path = os.path.join(folder_path, f'{code_name}.txt')
    run_flag = False
    if not os.path.exists(flag_path):
        run_flag = True
    else:
        # 如果文件的修改时间不是今天，需要重新跑
        md_time = datetime.datetime.fromtimestamp(os.path.getmtime(flag_path))
        if md_time.date() != datetime.datetime.now().date():
            run_flag = True

    if run_flag:
        strat_time = datetime.datetime.now()

        if code_name == '盘中择时':
            # 盘中择时需要把盘中选股结果同步到实盘选股结果中
            raw_path = root_path + '/data/系统缓存/早盘数据/'
            tar_path = runtime_code_path + '/rocket/data/系统缓存/早盘数据/'
            if os.path.exists(tar_path):
                shutil.rmtree(tar_path)  # 先删除目标文件夹
            shutil.copytree(raw_path, tar_path)
            raw_path = root_path + '/data/系统缓存/早盘择时/'
            tar_path = runtime_code_path + '/rocket/data/系统缓存/早盘择时/'
            if os.path.exists(tar_path):
                shutil.rmtree(tar_path)  # 先删除目标文件夹
            shutil.copytree(raw_path, tar_path)

        # 在 Windows 上使用 'gbk' 编码，Linux/Mac 使用 'utf-8'
        encoded_path = runtime_code_path.encode('gbk').decode('mbcs')  # Windows 专用
        command = f'set PYTHONPATH={encoded_path} && {python_exe} {encoded_path}/{code_name}.py select trading'
        Rb.record_log(f'【{code_name}】开始执行，命令：{command}', send=True)
        res = os.system(command)
        Rb.record_log(f'【{code_name}】已完成，状态码：{res}', send=True)
        # 同步交易计划
        syn_trading_plan()
        if res == 0:
            # 保存运行标识
            used_time = datetime.datetime.now() - strat_time
            save_flag(flag_path, used_time)
    else:
        Rb.record_log(f'【{code_name}】已运行，跳过', send=True)


def trading():
    # 在 Windows 上使用 'gbk' 编码，Linux/Mac 使用 'utf-8'
    command = f'{python_exe} {root_path}/program/startup.py'
    res = os.system(command)


def syn_trading_plan():
    # 将最新的交易计划同步给实盘
    raw_path = runtime_code_path + '/rocket/data/账户信息/交易计划.csv'
    tar_path = root_path + '/data/账户信息/交易计划.csv'
    if os.path.exists(raw_path):
        # 将文件复制到目标路径
        shutil.copy(raw_path, tar_path)


def run_async(job_func, *args, **kwargs):
    threading.Thread(target=job_func, args=args, kwargs=kwargs).start()


if __name__ == '__main__':
    # 框架代码的路径
    runtime_code_path = r''

    # 并行调度任务
    schedule.every().day.at("08:00").do(run_async, select_stock, code_name="实盘选股")
    schedule.every().day.at("09:00").do(run_async, trading)
    schedule.every().day.at("09:55").do(run_async, select_stock, code_name="盘中择时")

    # 手动立即执行（如果需要）
    now = datetime.datetime.now()
    if now > ExchangeAPI.get_time('08:00'):
        run_async(select_stock, code_name="实盘选股")
        time.sleep(5)
    if now > ExchangeAPI.get_time('09:00'):
        run_async(trading)
        time.sleep(5)
    if now > ExchangeAPI.get_time('09:55'):
        run_async(select_stock, code_name="盘中择时")
        time.sleep(5)

    while True:
        schedule.run_pending()
        time.sleep(1)
        now = datetime.datetime.now()
        if now >= ExchangeAPI.get_time('15:31'):
            # 到时间了强制退出
            exit(0)
