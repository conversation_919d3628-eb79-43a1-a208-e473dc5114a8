#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试 trading 进程监控功能
"""
import sys
import os
import time
import datetime
import subprocess
import psutil

# 添加项目路径到 sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟 RainbowV1 模块
class MockRb:
    @staticmethod
    def record_log(msg, send=False):
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {msg}")

# 导入修改后的函数
from importlib import import_module

def test_process_functions():
    """测试进程管理函数"""
    print("=== 测试进程管理功能 ===")
    
    # 模拟全局变量
    global trading_process, trading_process_start_time
    trading_process = None
    trading_process_start_time = None
    
    # 模拟 Rb 模块
    import program.RainbowV1 as Rb
    Rb.record_log = MockRb.record_log
    
    # 测试检查进程状态（无进程时）
    print("\n1. 测试检查进程状态（无进程时）")
    from 调度程序 import is_trading_running
    result = is_trading_running()
    print(f"进程运行状态: {result}")
    
    # 测试启动进程（模拟）
    print("\n2. 测试启动进程功能")
    # 这里我们不实际启动 startup.py，而是启动一个简单的 Python 进程
    python_exe = sys.executable
    test_script = """
import time
print("测试进程已启动")
for i in range(60):
    print(f"运行中... {i}")
    time.sleep(1)
"""
    
    # 创建临时测试脚本
    with open('temp_test_process.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    try:
        # 启动测试进程
        trading_process = subprocess.Popen(
            [python_exe, 'temp_test_process.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        trading_process_start_time = datetime.datetime.now()
        print(f"测试进程已启动，PID: {trading_process.pid}")
        
        # 等待一下让进程启动
        time.sleep(2)
        
        # 测试检查进程状态（有进程时）
        print("\n3. 测试检查进程状态（有进程时）")
        if trading_process.poll() is None:
            try:
                process = psutil.Process(trading_process.pid)
                cmdline = ' '.join(process.cmdline())
                print(f"进程命令行: {cmdline}")
                print(f"进程运行状态: True")
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"检查进程时出错: {e}")
        else:
            print("进程已结束")
        
        # 终止测试进程
        print("\n4. 终止测试进程")
        trading_process.terminate()
        trading_process.wait()
        print("测试进程已终止")
        
    finally:
        # 清理临时文件
        if os.path.exists('temp_test_process.py'):
            os.remove('temp_test_process.py')

if __name__ == '__main__':
    try:
        test_process_functions()
        print("\n=== 测试完成 ===")
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
